[{"constant": false, "inputs": [{"name": "number", "type": "uint256"}], "name": "fibonacciNotify", "outputs": [{"name": "result", "type": "uint256"}], "payable": false, "stateMutability": "nonpayable", "type": "function"}, {"constant": true, "inputs": [{"name": "number", "type": "uint256"}], "name": "<PERSON><PERSON><PERSON><PERSON>", "outputs": [{"name": "result", "type": "uint256"}], "payable": false, "stateMutability": "view", "type": "function"}, {"anonymous": false, "inputs": [{"indexed": false, "name": "input", "type": "uint256"}, {"indexed": false, "name": "result", "type": "uint256"}], "name": "Notify", "type": "event"}]