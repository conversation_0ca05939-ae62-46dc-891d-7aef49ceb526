# Straightforward HTTP client

Straightforward HTTP client based on PHP's Client URL Library

## Prerequisites

- PHP >= 7.1
- cURL and JSON extensions installed and enabled

## Installation

`composer require furqansiddiqui/http-client`

## Usage

### JSON RPC specs 1.0 & 2.0

This lib includes JSON RPC 1.0 & 2.0 clients

### HTTP Methods

This library supports `GET`,`POST`,`PUT`,`DELETE` HTTP methods
