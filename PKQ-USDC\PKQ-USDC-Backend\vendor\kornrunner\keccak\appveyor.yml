build: false
shallow_clone: true
clone_folder: c:\php-keccak
platform: x64

cache:
  - c:\php -> appveyor.yml
  - vendor -> composer.lock


environment:
  matrix:
    - PHP_VERSION: '7.3.7'
      PHP_DOWNLOAD_URL: 'https://windows.php.net/downloads/releases/archives/php-7.3.7-nts-Win32-VC15-x64.zip'
    - PHP_VERSION: '7.4.3'
      PHP_DOWNLOAD_URL: 'https://windows.php.net/downloads/releases/archives/php-7.4.3-nts-Win32-vc15-x64.zip'
    - PHP_VERSION: '8.0.0'
      PHP_DOWNLOAD_URL: 'https://windows.php.net/downloads/releases/php-8.0.0-nts-Win32-vs16-x64.zip'

matrix:
  fast_finish: true

init:
  - SET PATH=c:\php\%PHP_VERSION%;%PATH%
  - set COMPOSER_NO_INTERACTION=1

install:
  - IF NOT EXIST c:\php mkdir c:\php
  - IF NOT EXIST c:\php\%PHP_VERSION% mkdir c:\php\%PHP_VERSION%
  - cd c:\php\%PHP_VERSION%
  - IF NOT EXIST php-installed.txt curl --fail --location --silent --show-error -o php.zip %PHP_DOWNLOAD_URL%
  - IF NOT EXIST php-installed.txt 7z x php.zip -y
  - IF NOT EXIST php-installed.txt del /Q *.zip
  - IF NOT EXIST php-installed.txt copy /Y php.ini-development php.ini
  - IF NOT EXIST php-installed.txt echo max_execution_time=1200 >> php.ini
  - IF NOT EXIST php-installed.txt echo date.timezone="UTC" >> php.ini
  - IF NOT EXIST php-installed.txt echo extension_dir=ext >> php.ini
  - IF NOT EXIST php-installed.txt echo extension=php_openssl.dll >> php.ini
  - IF NOT EXIST php-installed.txt echo extension=php_mbstring.dll >> php.ini
  - IF NOT EXIST php-installed.txt appveyor DownloadFile https://getcomposer.org/composer.phar
  - IF NOT EXIST php-installed.txt echo @php %%~dp0composer.phar %%* > composer.bat
  - IF NOT EXIST php-installed.txt type nul >> php-installed.txt
  - cd c:\php-keccak

test_script:
  - composer install
  - vendor/bin/phpunit.bat --coverage-text

