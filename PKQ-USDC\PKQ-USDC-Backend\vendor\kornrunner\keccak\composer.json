{"name": "kornrunner/keccak", "description": "Pure PHP implementation of Keccak", "keywords": ["keccak", "sha-3", "sha3-256"], "license": "MIT", "authors": [{"name": "<PERSON>", "homepage": "https://github.com/kornrunner/php-keccak"}], "require": {"php": ">=7.3", "symfony/polyfill-mbstring": "^1.8"}, "autoload": {"psr-4": {"kornrunner\\": "src"}}, "autoload-dev": {"psr-4": {"kornrunner\\": "test"}}, "require-dev": {"phpunit/phpunit": "^8.2"}}